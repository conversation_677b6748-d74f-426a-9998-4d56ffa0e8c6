"""
Discovery agent for finding new tokens from multiple data sources.
Uses modern async patterns and intelligent caching.
"""

import asyncio
import hashlib
import time
from datetime import datetime, timedelta, timezone
from typing import Any

import aiohttp
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core.config import config
from ..core.http_manager import http_manager
from ..utils.error_handling import (
    fault_tolerant, API_CIRCUIT_CONFIG, EXTERNAL_SERVICE_RETRY_CONFIG
)
from ..utils.rate_limit import _global_rate_limiter

logger = structlog.get_logger(__name__)


class DiscoveryAgent:
    """
    Agent responsible for discovering new tokens from multiple sources.

    Data Sources:
    - DeFiLlama new listings
    - DEXScreener trending
    - CoinMarketCap new listings
    - Custom DEX monitoring
    """

    def __init__(self, db_manager, cache_manager, metrics_collector, coordinator):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.metrics_collector = metrics_collector
        self.coordinator = coordinator

        # HTTP sessions managed by centralized manager
        self.http_manager = http_manager

        # Tracking
        self.seen_tokens = set()
        self.last_discovery_time = None

        # Concurrency control per discovery operation (higher level than HTTP manager)
        self.discovery_semaphore = asyncio.Semaphore(5)  # Max 5 concurrent discovery operations

    async def initialize(self) -> None:
        """Initialize the discovery agent."""
        try:
            # HTTP sessions are managed by the centralized HTTP manager
            # No need to create individual sessions here

            # Load seen tokens from cache
            seen_tokens_data = await self.cache_manager.get("seen_tokens", "discovery")
            if seen_tokens_data:
                self.seen_tokens = set(seen_tokens_data)

            logger.info("Discovery agent initialized")

        except Exception as e:
            logger.error("Failed to initialize discovery agent", error=str(e))
            raise

    async def shutdown(self) -> None:
        """Shutdown the discovery agent."""
        try:
            # Save seen tokens to cache
            await self.cache_manager.set(
                "seen_tokens",
                list(self.seen_tokens),
                "discovery",
                ttl=86400,  # 24 hours
            )

            # HTTP sessions are managed by the centralized HTTP manager
            # No need to close individual sessions here

            logger.info("Discovery agent shutdown complete")

        except Exception as e:
            logger.error("Error during discovery agent shutdown", error=str(e))

    async def health_check(self) -> bool:
        """Perform health check."""
        try:
            # Get session from HTTP manager
            session = await self.http_manager.get_session("default")
            semaphore = await self.http_manager.acquire_semaphore("default")

            # Test a simple request
            async with semaphore:
                async with session.get("https://httpbin.org/status/200") as response:
                    return response.status == 200

        except Exception as e:
            logger.error("Discovery agent health check failed", error=str(e))
            return False

    # ==================== MAIN DISCOVERY METHODS ====================

    async def discover_tokens(
        self, sources: list[str], min_age_hours: int = 24, limit: int = 50
    ) -> dict[str, Any]:
        """
        Discover new tokens from specified sources.

        Args:
            sources: List of data sources to use
            min_age_hours: Minimum token age in hours
            limit: Maximum number of tokens to return

        Returns:
            Dict containing discovered tokens and metadata
        """
        start_time = datetime.now(timezone.utc)
        discovered_tokens = []
        source_results = {}
        errors = []

        try:
            logger.info(
                "Starting token discovery",
                sources=sources,
                min_age_hours=min_age_hours,
                limit=limit,
            )

            # Run discovery from all sources concurrently
            tasks = []
            for source in sources:
                if hasattr(self, f"_discover_from_{source}"):
                    method = getattr(self, f"_discover_from_{source}")
                    tasks.append(
                        self._run_discovery_source(source, method, min_age_hours, limit)
                    )
                else:
                    logger.warning(f"Unknown discovery source: {source}")

            # Wait for all sources to complete
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)

                for i, result in enumerate(results):
                    source = sources[i] if i < len(sources) else f"unknown_{i}"

                    if isinstance(result, Exception):
                        error_msg = f"Source {source} failed: {str(result)}"
                        errors.append(error_msg)
                        logger.error(error_msg, source=source, error=str(result))
                    else:
                        source_results[source] = result
                        if result.get("tokens"):
                            discovered_tokens.extend(result["tokens"])

            # Remove duplicates and filter
            unique_tokens = self._deduplicate_tokens(discovered_tokens)
            filtered_tokens = await self._filter_tokens(unique_tokens, min_age_hours)

            # Limit results
            final_tokens = filtered_tokens[:limit]

            # Implement fallback discovery if we didn't get enough tokens
            if len(final_tokens) < limit // 2:  # If we got less than half the requested tokens
                logger.info(f"Only found {len(final_tokens)} tokens, attempting fallback discovery...")

                # Enhanced fallback sources with priority order
                fallback_sources = []

                # Primary fallbacks based on what was already tried
                if "dexscreener" in sources:
                    # If DexScreener was used, try CoinGecko and Birdeye
                    if "coingecko" not in sources:
                        fallback_sources.append("coingecko")
                    if "birdeye" not in sources:
                        fallback_sources.append("birdeye")
                    if "defillama" not in sources:
                        fallback_sources.append("defillama")

                elif "coingecko" in sources:
                    # If CoinGecko was used, try DexScreener and Birdeye
                    if "dexscreener" not in sources:
                        fallback_sources.append("dexscreener")
                    if "birdeye" not in sources:
                        fallback_sources.append("birdeye")

                else:
                    # If no major source was used, try all primary sources
                    fallback_sources = ["dexscreener", "coingecko", "birdeye", "defillama"]

                if fallback_sources:
                    logger.info(f"Trying fallback sources: {fallback_sources}")
                    fallback_tasks = []
                    for source in fallback_sources:
                        if hasattr(self, f"_discover_from_{source}"):
                            method = getattr(self, f"_discover_from_{source}")
                            fallback_tasks.append(
                                self._run_discovery_source(source, method, min_age_hours, limit - len(final_tokens))
                            )

                    if fallback_tasks:
                        fallback_results = await asyncio.gather(*fallback_tasks, return_exceptions=True)
                        fallback_tokens = []

                        for result in fallback_results:
                            if not isinstance(result, Exception) and result.get("tokens"):
                                fallback_tokens.extend(result["tokens"])

                        # Process fallback tokens
                        if fallback_tokens:
                            unique_fallback = self._deduplicate_tokens(fallback_tokens)
                            filtered_fallback = await self._filter_tokens(unique_fallback, min_age_hours)

                            # Add fallback tokens to final results
                            for token in filtered_fallback:
                                token_key = f"{token['address']}_{token['chain']}"
                                if token_key not in self.seen_tokens:
                                    final_tokens.append(token)
                                    if len(final_tokens) >= limit:
                                        break

                            logger.info(f"Fallback discovery added {len(filtered_fallback)} tokens")

                            # Multi-source validation for high-confidence tokens
                            if len(final_tokens) > 0:
                                validated_tokens = await self._validate_tokens_multi_source(final_tokens[:5])  # Validate top 5
                                logger.info(f"Multi-source validation completed for {len(validated_tokens)} tokens")

            # Update tracking
            for token in final_tokens:
                token_key = f"{token['address']}_{token['chain']}"
                self.seen_tokens.add(token_key)

            # Cache results
            await self.cache_manager.set(
                "last_discovery_result",
                {
                    "tokens": final_tokens,
                    "source_results": source_results,
                    "timestamp": start_time.isoformat(),
                },
                "discovery",
                ttl=300,  # 5 minutes
            )

            duration = (datetime.now(timezone.utc) - start_time).total_seconds()

            logger.info(
                "Token discovery completed",
                tokens_found=len(final_tokens),
                sources_used=len(source_results),
                errors_count=len(errors),
                duration_seconds=duration,
            )

            return {
                "tokens": final_tokens,
                "source_results": source_results,
                "errors": errors,
                "duration_seconds": duration,
                "discovered_at": start_time.isoformat(),
            }

        except Exception as e:
            logger.error("Token discovery failed", error=str(e))
            raise

    async def _run_discovery_source(
        self, source: str, method, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Run discovery for a specific source with error handling."""
        try:
            result = await method(min_age_hours, limit)

            await self.metrics_collector.record_discovery_source(
                source=source, tokens_found=len(result.get("tokens", [])), success=True
            )

            return result

        except Exception as e:
            await self.metrics_collector.record_discovery_source(
                source=source, tokens_found=0, success=False, error=str(e)
            )
            raise

    # ==================== DATA SOURCE IMPLEMENTATIONS ====================

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_defillama(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from DeFiLlama."""
        try:
            # Use HTTP manager for DeFiLlama
            session = await self.http_manager.get_session("default")
            semaphore = await self.http_manager.acquire_semaphore("default")

            async with semaphore:
                # Get recent protocols
                url = "https://api.llama.fi/protocols"
                start_time = time.time()
                async with session.get(url) as response:
                    response.raise_for_status()
                    protocols = await response.json()

                tokens = []
                datetime.now(timezone.utc) - timedelta(hours=min_age_hours)

                for protocol in protocols[: limit * 2]:  # Get extra for filtering
                    # Basic token info (DeFiLlama doesn't provide direct token discovery)
                    # This is a simplified implementation
                    if "symbol" in protocol and "name" in protocol:
                        token = {
                            "symbol": protocol["symbol"],
                            "name": protocol["name"],
                            "address": protocol.get("address", ""),
                            "chain": protocol.get("chain", "ethereum").lower(),
                            "price_usd": 0.0,  # Would need additional API call
                            "market_cap_usd": protocol.get("tvl", 0),
                            "volume_24h_usd": 0.0,
                            "source": "defillama",
                            "discovered_at": datetime.now(timezone.utc),
                        }

                        # Skip if we've seen this token
                        token_key = f"{token['address']}_{token['chain']}"
                        if token_key not in self.seen_tokens:
                            tokens.append(token)

                return {
                    "tokens": tokens[:limit],
                    "source": "defillama",
                    "total_checked": len(protocols),
                }

        except Exception as e:
            logger.error("DeFiLlama discovery failed", error=str(e))
            raise

    @fault_tolerant("dexscreener_discovery", EXTERNAL_SERVICE_RETRY_CONFIG, API_CIRCUIT_CONFIG)
    async def _discover_from_dexscreener(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from DEXScreener using reliable search endpoints."""
        try:
            # Use discovery-level concurrency control
            async with self.discovery_semaphore:
                tokens = []
                total_checked = 0

                # Get optimized session for DexScreener
                session = await self.http_manager.get_session("dexscreener")
                semaphore = await self.http_manager.acquire_semaphore("dexscreener")

                # Enhanced search strategies for better token discovery
                search_strategies = [
                    # Search for trending tokens (more likely to find new tokens)
                    {"url": "https://api.dexscreener.com/latest/dex/pairs/ethereum", "params": {}, "chain": "ethereum"},
                    {"url": "https://api.dexscreener.com/latest/dex/pairs/bsc", "params": {}, "chain": "bsc"},
                    {"url": "https://api.dexscreener.com/latest/dex/pairs/polygon", "params": {}, "chain": "polygon"},
                    # Fallback to search if pairs endpoints don't work
                    {"url": "https://api.dexscreener.com/latest/dex/search", "params": {"q": "volume"}, "chain": "multi"},
                    {"url": "https://api.dexscreener.com/latest/dex/search", "params": {"q": "new"}, "chain": "multi"},
                ]

                for strategy in search_strategies:
                    try:
                        url = strategy["url"]
                        params = strategy.get("params", {})

                        await _global_rate_limiter.acquire("dexscreener")

                        # Implement retry logic with exponential backoff
                        for attempt in range(3):
                            try:
                                # Use HTTP manager's session and semaphore
                                async with semaphore:
                                    start_time = time.time()
                                    async with session.get(url, params=params) as response:
                                        if response.status == 200:
                                            data = await response.json()
                                            pairs = data.get("pairs", [])

                                            if pairs:
                                                logger.info(f"DexScreener: Found {len(pairs)} pairs from {url} (attempt {attempt + 1})")
                                                total_checked += len(pairs)

                                                # Record successful request
                                                response_time = time.time() - start_time
                                                self.http_manager.record_request_result("dexscreener", True, response_time)
                                                break  # Success, exit retry loop
                                        elif response.status == 429:  # Rate limited
                                            wait_time = 2 ** attempt
                                            logger.warning(f"DexScreener rate limited, waiting {wait_time}s before retry")
                                            await asyncio.sleep(wait_time)
                                            continue
                                        else:
                                            logger.warning(f"DexScreener returned status {response.status} for {url}")
                                            if attempt == 2:  # Last attempt
                                                pairs = []
                                            continue
                            except asyncio.TimeoutError:
                                logger.warning(f"DexScreener timeout for {url} (attempt {attempt + 1})")
                                response_time = time.time() - start_time
                                self.http_manager.record_request_result("dexscreener", False, response_time)
                                if attempt == 2:  # Last attempt
                                    pairs = []
                                await asyncio.sleep(2 ** attempt)
                                continue
                            except Exception as e:
                                logger.warning(f"DexScreener error for {url} (attempt {attempt + 1}): {e}")
                                response_time = time.time() - start_time
                                self.http_manager.record_request_result("dexscreener", False, response_time)
                                if attempt == 2:  # Last attempt
                                    pairs = []
                                await asyncio.sleep(2 ** attempt)
                                continue

                        # Process pairs and extract tokens (after successful API call)
                        if 'pairs' in locals() and pairs:
                            for pair in pairs[:limit//len(search_strategies)]:
                                base_token = pair.get("baseToken", {})

                                if base_token.get("address") and base_token.get("symbol"):
                                    # Filter for quality tokens
                                    volume_24h = float(pair.get("volume", {}).get("h24", 0))
                                    liquidity_usd = float(pair.get("liquidity", {}).get("usd", 0))

                                    # Reasonable quality filters for production
                                    if volume_24h < 10 or liquidity_usd < 500:
                                        continue

                                    token = {
                                        "symbol": base_token.get("symbol", ""),
                                        "name": base_token.get("name", ""),
                                        "address": base_token["address"],
                                        "chain": pair.get("chainId", "ethereum").lower(),
                                        "price_usd": float(pair.get("priceUsd", 0)),
                                        "market_cap_usd": float(pair.get("marketCap", 0)),
                                        "volume_24h_usd": volume_24h,
                                        "liquidity_usd": liquidity_usd,
                                        "price_change_24h": float(pair.get("priceChange", {}).get("h24", 0)),
                                        "dex": pair.get("dexId", ""),
                                        "pair_address": pair.get("pairAddress", ""),
                                        "source": "dexscreener",
                                        "discovered_at": datetime.now(timezone.utc),
                                    }

                                    # Skip if we've seen this token
                                    token_key = f"{token['address']}_{token['chain']}"
                                    if token_key not in self.seen_tokens:
                                        tokens.append(token)
                                        self.seen_tokens.add(token_key)

                                        if len(tokens) >= limit:
                                            break

                            if len(tokens) >= limit:
                                break

                    except Exception as e:
                        logger.warning(f"DexScreener strategy failed for {strategy['url']}: {e}")
                        continue

                logger.info(f"DexScreener discovery completed: {len(tokens)} tokens from {total_checked} pairs")

                return {
                    "tokens": tokens,
                    "source": "dexscreener",
                    "total_checked": total_checked,
                }

        except Exception as e:
            logger.error("DEXScreener discovery failed", error=str(e))
            raise

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_coinmarketcap(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from CoinMarketCap."""
        try:
            # Use HTTP manager for CoinMarketCap
            session = await self.http_manager.get_session("default")
            semaphore = await self.http_manager.acquire_semaphore("default")

            async with semaphore:
                # Note: This would require CMC API key in production
                # Using public endpoint with limitations
                url = "https://api.coinmarketcap.com/data-api/v3/cryptocurrency/listing"
                params = {
                    "start": 1,
                    "limit": limit,
                    "sortBy": "date_added",
                    "sortType": "desc",
                    "convert": "USD",
                    "cryptoType": "all",
                    "tagType": "all",
                }

                start_time = time.time()
                async with session.get(url, params=params) as response:
                    response.raise_for_status()
                    data = await response.json()

                tokens = []

                for item in data.get("data", {}).get("cryptoCurrencyList", []):
                    token_data = {
                        "symbol": item.get("symbol", ""),
                        "name": item.get("name", ""),
                        "address": "",  # CMC doesn't provide contract addresses in this endpoint
                        "chain": "ethereum",  # Default assumption
                        "price_usd": float(item.get("quotes", [{}])[0].get("price", 0)),
                        "market_cap_usd": float(
                            item.get("quotes", [{}])[0].get("marketCap", 0)
                        ),
                        "volume_24h_usd": float(
                            item.get("quotes", [{}])[0].get("volume24h", 0)
                        ),
                        "source": "coinmarketcap",
                        "discovered_at": datetime.now(timezone.utc),
                    }

                    tokens.append(token_data)

                return {
                    "tokens": tokens,
                    "source": "coinmarketcap",
                    "total_checked": len(
                        data.get("data", {}).get("cryptoCurrencyList", [])
                    ),
                }

        except Exception as e:
            logger.error("CoinMarketCap discovery failed", error=str(e))
            raise

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_dextools(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from DexTools."""
        try:
            # Use HTTP manager for DexTools
            session = await self.http_manager.get_session("default")
            semaphore = await self.http_manager.acquire_semaphore("default")

            async with semaphore:
                # Get trending tokens from DexTools
                url = "https://www.dextools.io/shared/data/trending"
                headers = {
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                    "Accept": "application/json",
                }

                start_time = time.time()
                async with session.get(url, headers=headers) as response:
                    response.raise_for_status()
                    data = await response.json()

                tokens = []
                current_time = datetime.now(timezone.utc)
                min_created_at = current_time - timedelta(hours=min_age_hours)

                for token_data in data.get("data", [])[:limit * 2]:  # Get more to filter
                    if token_data.get("address") and token_data.get("chain"):
                        # Parse creation time if available
                        created_at = None
                        if token_data.get("creationTime"):
                            try:
                                created_at = datetime.fromtimestamp(token_data["creationTime"] / 1000, tz=timezone.utc)
                            except (ValueError, TypeError):
                                pass

                        # Apply age filter if creation time is available
                        if created_at and created_at < min_created_at:
                            continue

                        token = {
                            "symbol": token_data.get("symbol", ""),
                            "name": token_data.get("name", ""),
                            "address": token_data["address"],
                            "chain": token_data["chain"].lower(),
                            "price_usd": float(token_data.get("price", 0)),
                            "market_cap_usd": float(token_data.get("mcap", 0)),
                            "volume_24h_usd": float(token_data.get("volume24h", 0)),
                            "liquidity_usd": float(token_data.get("liquidity", 0)),
                            "source": "dextools",
                            "discovered_at": current_time,
                            "created_at": created_at,
                        }

                        # Skip if we've seen this token
                        token_key = f"{token['address']}_{token['chain']}"
                        if token_key not in self.seen_tokens:
                            tokens.append(token)
                            self.seen_tokens.add(token_key)

                return {
                    "tokens": tokens[:limit],
                    "source": "dextools",
                    "total_checked": len(data.get("data", [])),
                }

        except Exception as e:
            logger.error("DexTools discovery failed", error=str(e))
            raise

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_coingecko(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover trending tokens from CoinGecko."""
        try:
            # Use HTTP manager for CoinGecko
            session = await self.http_manager.get_session("coingecko")
            semaphore = await self.http_manager.acquire_semaphore("coingecko")

            async with semaphore:
                # Get trending tokens from CoinGecko
                url = "https://api.coingecko.com/api/v3/search/trending"
                headers = {
                    "Accept": "application/json",
                    "User-Agent": "TokenAnalyzer/1.0"
                }

                start_time = time.time()
                async with session.get(url, headers=headers) as response:
                    response.raise_for_status()
                    data = await response.json()

                    # Record successful request
                    response_time = time.time() - start_time
                    self.http_manager.record_request_result("coingecko", True, response_time)

                tokens = []
                current_time = datetime.now(datetime.timezone.utc)

                # Get trending coins
                for coin_data in data.get("coins", [])[:limit]:
                    coin_id = coin_data.get("item", {}).get("id")
                    if not coin_id:
                        continue

                    # Get detailed coin info
                    detail_url = f"https://api.coingecko.com/api/v3/coins/{coin_id}"
                    await asyncio.sleep(0.1)  # Rate limiting

                    async with self.session.get(detail_url, headers=headers) as detail_response:
                        if detail_response.status == 200:
                            detail_data = await detail_response.json()

                            # Extract contract addresses
                            platforms = detail_data.get("platforms", {})
                            for platform, address in platforms.items():
                                if address and platform in ["ethereum", "polygon-pos", "binance-smart-chain"]:
                                    token = {
                                        "symbol": detail_data.get("symbol", "").upper(),
                                        "name": detail_data.get("name", ""),
                                        "address": address,
                                        "chain": platform.replace("-pos", "").replace("binance-smart-chain", "bsc"),
                                        "price_usd": float(detail_data.get("market_data", {}).get("current_price", {}).get("usd", 0)),
                                        "market_cap_usd": float(detail_data.get("market_data", {}).get("market_cap", {}).get("usd", 0)),
                                        "volume_24h_usd": float(detail_data.get("market_data", {}).get("total_volume", {}).get("usd", 0)),
                                        "source": "coingecko",
                                        "discovered_at": current_time,
                                    }

                                    # Skip if we've seen this token
                                    token_key = f"{token['address']}_{token['chain']}"
                                    if token_key not in self.seen_tokens:
                                        tokens.append(token)
                                        self.seen_tokens.add(token_key)

                return {
                    "tokens": tokens[:limit],
                    "source": "coingecko",
                    "total_checked": len(data.get("coins", [])),
                }

        except Exception as e:
            # Record failed request
            if 'start_time' in locals():
                response_time = time.time() - start_time
                self.http_manager.record_request_result("coingecko", False, response_time)

            logger.error("CoinGecko discovery failed", error=str(e))
            raise

    @fault_tolerant("birdeye_discovery", EXTERNAL_SERVICE_RETRY_CONFIG, API_CIRCUIT_CONFIG)
    async def _discover_from_birdeye(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from Birdeye."""
        try:
            # Use HTTP manager for Birdeye
            session = await self.http_manager.get_session("birdeye")
            semaphore = await self.http_manager.acquire_semaphore("birdeye")

            async with semaphore:
                await _global_rate_limiter.acquire("birdeye")
                # Get trending tokens from Birdeye (Solana focused)
                url = "https://public-api.birdeye.so/defi/tokenlist"
                headers = {
                    "Accept": "application/json",
                    "User-Agent": "TokenAnalyzer/1.0"
                }

                # Add API key if available
                if config.api.birdeye_api_key:
                    headers["X-API-KEY"] = config.api.birdeye_api_key.get_secret_value()
                params = {
                    "sort_by": "v24hUSD",
                    "sort_type": "desc",
                    "offset": 0,
                    "limit": limit * 2  # Get more to filter
                }

                start_time = time.time()
                async with session.get(url, headers=headers, params=params) as response:
                    response.raise_for_status()
                    data = await response.json()

                tokens = []
                current_time = datetime.now(timezone.utc)
                min_created_at = current_time - timedelta(hours=min_age_hours)

                for token_data in data.get("data", {}).get("tokens", []):
                    if token_data.get("address"):
                        # Parse creation time if available
                        created_at = None
                        if token_data.get("createdTime"):
                            try:
                                created_at = datetime.fromtimestamp(token_data["createdTime"] / 1000, tz=timezone.utc)
                            except (ValueError, TypeError):
                                pass

                        # Apply age filter if creation time is available
                        if created_at and created_at < min_created_at:
                            continue

                        token = {
                            "symbol": token_data.get("symbol", ""),
                            "name": token_data.get("name", ""),
                            "address": token_data["address"],
                            "chain": "solana",  # Birdeye is primarily Solana
                            "price_usd": float(token_data.get("price", 0)),
                            "market_cap_usd": float(token_data.get("mc", 0)),
                            "volume_24h_usd": float(token_data.get("v24hUSD", 0)),
                            "liquidity_usd": float(token_data.get("liquidity", 0)),
                            "source": "birdeye",
                            "discovered_at": current_time,
                            "created_at": created_at,
                        }

                        # Skip if we've seen this token
                        token_key = f"{token['address']}_{token['chain']}"
                        if token_key not in self.seen_tokens:
                            tokens.append(token)
                            self.seen_tokens.add(token_key)

                # Record successful request
                response_time = time.time() - start_time
                self.http_manager.record_request_result("birdeye", True, response_time)

                return {
                    "tokens": tokens[:limit],
                    "source": "birdeye",
                    "total_checked": len(data.get("data", {}).get("tokens", [])),
                }

        except Exception as e:
            # Record failed request
            if 'start_time' in locals():
                response_time = time.time() - start_time
                self.http_manager.record_request_result("birdeye", False, response_time)

            logger.error("Birdeye discovery failed", error=str(e))
            raise

    async def _validate_tokens_multi_source(self, tokens: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """
        Validate tokens across multiple sources for higher confidence.

        Args:
            tokens: List of tokens to validate

        Returns:
            List of validated tokens with confidence scores
        """
        validated_tokens = []

        for token in tokens:
            try:
                validation_sources = []
                confidence_score = 0

                # Check token across different APIs
                address = token.get("address")
                chain = token.get("chain", "ethereum")

                if not address:
                    continue

                # Validate with CoinGecko (if not the original source)
                if token.get("source") != "coingecko":
                    try:
                        session = await self.http_manager.get_session("coingecko")
                        semaphore = await self.http_manager.acquire_semaphore("coingecko")

                        async with semaphore:
                            # Simple validation - check if token exists
                            url = f"https://api.coingecko.com/api/v3/coins/{chain}/contract/{address}"
                            async with session.get(url) as response:
                                if response.status == 200:
                                    validation_sources.append("coingecko")
                                    confidence_score += 30
                    except Exception:
                        pass  # Validation failed, continue

                # Validate with DexScreener (if not the original source)
                if token.get("source") != "dexscreener":
                    try:
                        session = await self.http_manager.get_session("dexscreener")
                        semaphore = await self.http_manager.acquire_semaphore("dexscreener")

                        async with semaphore:
                            url = f"https://api.dexscreener.com/latest/dex/tokens/{address}"
                            async with session.get(url) as response:
                                if response.status == 200:
                                    data = await response.json()
                                    if data.get("pairs"):
                                        validation_sources.append("dexscreener")
                                        confidence_score += 40
                    except Exception:
                        pass  # Validation failed, continue

                # Add base confidence from original source
                confidence_score += 30

                # Only include tokens with reasonable confidence
                if confidence_score >= 50:
                    token["validation_sources"] = validation_sources
                    token["confidence_score"] = confidence_score
                    validated_tokens.append(token)

            except Exception as e:
                logger.warning(f"Token validation failed for {token.get('address', 'unknown')}: {e}")
                continue

        return validated_tokens

    async def discover_trending_tokens(
        self, limit: int = 10, min_volume: int = 1000000, chains: list[str] = None
    ) -> dict[str, Any]:
        """
        Discover trending tokens (wrapper for discover_tokens with trending sources).

        Args:
            limit: Maximum number of tokens to return
            min_volume: Minimum 24h volume in USD
            chains: List of blockchain networks to search

        Returns:
            Dict containing discovered trending tokens and metadata
        """
        # Use trending-focused sources with new multi-source integration
        trending_sources = ["coingecko", "dexscreener", "dextools", "birdeye", "coinmarketcap"]

        # Filter sources based on chains if specified
        if chains:
            # Map chains to appropriate sources
            chain_sources = []
            if "ethereum" in chains:
                chain_sources.extend(["coingecko", "dexscreener", "dextools"])
            if "polygon" in chains or "bsc" in chains:
                chain_sources.extend(["dexscreener", "dextools"])
            if "solana" in chains:
                chain_sources.extend(["birdeye"])

            trending_sources = (
                list(set(trending_sources) & set(chain_sources))
                if chain_sources
                else trending_sources
            )

        # Discover tokens using the main discovery method
        result = await self.discover_tokens(
            sources=trending_sources,
            min_age_hours=1,  # Very recent for trending
            limit=limit,
        )

        # Filter by minimum volume if specified
        if min_volume > 0 and "tokens" in result:
            filtered_tokens = []
            for token in result["tokens"]:
                volume_24h = token.get("volume_24h", 0)
                if volume_24h >= min_volume:
                    filtered_tokens.append(token)

            result["tokens"] = filtered_tokens[:limit]
            result["filtered_by_volume"] = min_volume

        return result

    # ==================== UTILITY METHODS ====================

    def _deduplicate_tokens(self, tokens: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """Remove duplicate tokens based on address and chain."""
        seen = set()
        unique_tokens = []

        for token in tokens:
            # Create a unique key
            if token.get("address"):
                key = f"{token['address']}_{token['chain']}"
            else:
                # For tokens without address, use symbol + name + chain
                key = f"{token.get('symbol', '')}_{token.get('name', '')}_{token['chain']}"

            key_hash = hashlib.md5(key.encode()).hexdigest()

            if key_hash not in seen:
                seen.add(key_hash)
                unique_tokens.append(token)

        return unique_tokens

    async def _filter_tokens(
        self, tokens: list[dict[str, Any]], min_age_hours: int
    ) -> list[dict[str, Any]]:
        """Filter tokens based on age and other criteria."""
        filtered_tokens = []
        min_created_at = datetime.now(timezone.utc) - timedelta(hours=min_age_hours)

        for token in tokens:
            # Basic filtering
            if not token.get("symbol") or not token.get("address"):
                continue

            # Skip tokens that are too new (if we have age data)
            if "age_days" in token and token["age_days"] is not None:
                if token["age_days"] * 24 < min_age_hours:
                    continue

            # Skip tokens with very low liquidity (if available) - reasonable threshold
            if "liquidity_usd" in token and token["liquidity_usd"] < 500:
                continue

            filtered_tokens.append(token)

        return filtered_tokens

    async def get_metrics(self) -> dict[str, Any]:
        """Get discovery agent metrics."""
        return {
            "seen_tokens_count": len(self.seen_tokens),
            "last_discovery_time": self.last_discovery_time.isoformat()
            if self.last_discovery_time
            else None,
            "session_open": self.session is not None and not self.session.closed,
        }
