"""
Agent coordinator for orchestrating the multi-agent token analysis pipeline.
Manages agent execution, coordination, and communication.
"""

import asyncio
import uuid
from collections.abc import Callable
from datetime import datetime, timezone
from typing import Any

import aiohttp
import structlog

from ..core.cache import CacheManager
from ..core.config import config
from ..core.database import DatabaseManager
from ..integrations.metrics import MetricsCollector
from .analyst import AnalystAgent
from .audit import AuditAgent
from .chain_info import ChainInfoAgent
from .discovery import DiscoveryAgent
from .market_data import MarketDataAgent
from .scheduler import SchedulerAgent
from .sentiment import SentimentAgent
from .technical import TechnicalAgent
from .validator import ValidatorAgent

logger = structlog.get_logger(__name__)


class PipelineError:
    """Structured error information for pipeline failures."""

    def __init__(
        self,
        message: str,
        error_code: str = "PIPELINE_ERROR",
        agent: str | None = None,
        timestamp: datetime | None = None,
        details: dict[str, Any] | None = None
    ):
        self.message = message
        self.error_code = error_code
        self.agent = agent
        self.timestamp = timestamp or datetime.now(timezone.utc)
        self.details = details or {}

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "message": self.message,
            "error_code": self.error_code,
            "agent": self.agent,
            "timestamp": self.timestamp.isoformat(),
            "details": self.details
        }

    @classmethod
    def from_exception(
        cls,
        exception: Exception,
        agent: str | None = None,
        error_code: str = "EXCEPTION"
    ) -> "PipelineError":
        """Create error from exception."""
        return cls(
            message=str(exception),
            error_code=error_code,
            agent=agent,
            details={"exception_type": type(exception).__name__}
        )


class PipelineResult:
    """Pipeline execution result with enhanced error tracking and serialization."""

    def __init__(self, pipeline_id: str):
        self.pipeline_id = pipeline_id
        self.start_time = datetime.now(timezone.utc)
        self.end_time: datetime | None = None
        self.tokens: list[dict[str, Any]] = []
        self.analyses: list[dict[str, Any]] = []
        self.errors: list[PipelineError] = []
        self.agent_results: dict[str, Any] = {}
        self.success = False

    def complete(self, success: bool = True):
        """Mark pipeline as complete."""
        self.end_time = datetime.utcnow()
        self.success = success

    def add_error(self, error: str | Exception | PipelineError, agent: str | None = None):
        """Add error to pipeline result."""
        if isinstance(error, str):
            self.errors.append(PipelineError(message=error, agent=agent))
        elif isinstance(error, Exception):
            self.errors.append(PipelineError.from_exception(error, agent=agent))
        elif isinstance(error, PipelineError):
            self.errors.append(error)
        else:
            self.errors.append(PipelineError(message=str(error), agent=agent))

    @property
    def duration_seconds(self) -> float:
        """Get pipeline duration in seconds."""
        if self.end_time is None:
            return (datetime.utcnow() - self.start_time).total_seconds()
        return (self.end_time - self.start_time).total_seconds()

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "pipeline_id": self.pipeline_id,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "duration_seconds": self.duration_seconds,
            "tokens": self.tokens,
            "analyses": self.analyses,
            "errors": [error.to_dict() for error in self.errors],
            "agent_results": self.agent_results,
            "success": self.success
        }


class AgentCoordinator:
    """
    Coordinates the execution of multiple agents in the token analysis pipeline.

    Features:
    - Pipeline orchestration
    - Agent health monitoring
    - Error handling and recovery
    - Progress tracking
    - Result aggregation
    """

    def __init__(
        self,
        db_manager: DatabaseManager,
        cache_manager: CacheManager,
        metrics_collector: MetricsCollector,
    ):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.metrics_collector = metrics_collector

        # Initialize agents
        self.agents = {}
        self.running_pipelines: dict[str, PipelineResult] = {}
        self.monitoring_tasks: list[asyncio.Task] = []

        # Circuit breaker for each agent
        self.agent_circuit_breakers = {}

    async def initialize(self) -> None:
        """Initialize all agents."""
        try:
            # Create shared HTTP session for agents
            self.session = aiohttp.ClientSession()

            # Initialize agents with their specific required parameters
            self.agents = {
                "discovery": DiscoveryAgent(
                    db_manager=self.db_manager,
                    cache_manager=self.cache_manager,
                    metrics_collector=self.metrics_collector,
                    coordinator=self,
                ),
                "validator": ValidatorAgent(
                    db_manager=self.db_manager,
                    cache_manager=self.cache_manager,
                    session=self.session,
                ),
                "chain_info": ChainInfoAgent(
                    db_manager=self.db_manager,
                    cache_manager=self.cache_manager,
                    session=self.session,
                ),
                "market_data": MarketDataAgent(
                    db_manager=self.db_manager,
                    cache_manager=self.cache_manager,
                    session=self.session,
                ),
                "technical": TechnicalAgent(
                    db_manager=self.db_manager, cache_manager=self.cache_manager
                ),
                "sentiment": SentimentAgent(
                    db_manager=self.db_manager,
                    cache_manager=self.cache_manager,
                    session=self.session,
                ),
                "analyst": AnalystAgent(
                    db_manager=self.db_manager,
                    cache_manager=self.cache_manager,
                    metrics_collector=self.metrics_collector,
                ),
                "audit": AuditAgent(
                    db_manager=self.db_manager,
                    cache_manager=self.cache_manager,
                    metrics_collector=self.metrics_collector,
                ),
                "scheduler": SchedulerAgent(
                    db_manager=self.db_manager,
                    cache_manager=self.cache_manager,
                    metrics_collector=self.metrics_collector,
                ),
            }

            # Initialize each agent (only call initialize if the agent has this method)
            for name, agent in self.agents.items():
                try:
                    # Check if the agent has an initialize method
                    if hasattr(agent, "initialize") and callable(agent.initialize):
                        await agent.initialize()
                        logger.info(f"Agent {name} initialized successfully")
                    else:
                        logger.info(f"Agent {name} does not require initialization")

                    # Set up circuit breaker for all agents
                    self.agent_circuit_breakers[name] = {
                        "failures": 0,
                        "last_failure": None,
                        "threshold": 3,
                        "timeout": 300,  # 5 minutes
                    }
                except Exception as e:
                    logger.error(f"Failed to initialize agent {name}", error=str(e))
                    raise

            # Start scheduler if enabled
            if config.system.enable_scheduler:
                await self._start_scheduler()

            logger.info("Agent coordinator initialized successfully")

        except Exception as e:
            logger.error("Failed to initialize agent coordinator", error=str(e))
            raise

    async def shutdown(self) -> None:
        """Shutdown all agents and cleanup."""
        try:
            # Cancel monitoring tasks
            for task in self.monitoring_tasks:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            # Shutdown agents (only call shutdown if the agent has this method)
            for name, agent in self.agents.items():
                try:
                    # Check if the agent has a shutdown method
                    if hasattr(agent, "shutdown") and callable(agent.shutdown):
                        await agent.shutdown()
                        logger.info(f"Agent {name} shutdown successfully")
                    else:
                        logger.info(f"Agent {name} does not require shutdown")
                except Exception as e:
                    logger.error(f"Error shutting down agent {name}", error=str(e))

            logger.info("Agent coordinator shutdown complete")

        except Exception as e:
            logger.error("Error during coordinator shutdown", error=str(e))

    # ==================== PIPELINE EXECUTION ====================

    async def run_discovery_pipeline(
        self, sources: list[str], min_age_hours: int = 24, limit: int = 50
    ) -> dict[str, Any]:
        """Run token discovery pipeline."""
        pipeline_id = str(uuid.uuid4())
        result = PipelineResult(pipeline_id)
        self.running_pipelines[pipeline_id] = result

        try:
            logger.info("Starting discovery pipeline", pipeline_id=pipeline_id)

            # Discovery agent
            discovery_result = await self._run_agent_with_circuit_breaker(
                "discovery",
                "discover_tokens",
                sources=sources,
                min_age_hours=min_age_hours,
                limit=limit,
            )

            if not discovery_result["success"]:
                raise Exception(f"Discovery failed: {discovery_result['error']}")

            tokens = discovery_result["data"]["tokens"]
            result.tokens = tokens
            result.agent_results["discovery"] = discovery_result

            # Validation agent
            if tokens:
                validation_result = await self._run_agent_with_circuit_breaker(
                    "validator", "validate_tokens", tokens=tokens
                )

                if validation_result["success"]:
                    result.tokens = validation_result["data"]["valid_tokens"]
                    result.agent_results["validation"] = validation_result

            result.complete(success=True)

            logger.info(
                "Discovery pipeline completed",
                pipeline_id=pipeline_id,
                tokens_found=len(result.tokens),
                duration=result.duration_seconds,
            )

            return {
                "pipeline_id": pipeline_id,
                "tokens": result.tokens,
                "duration_seconds": result.duration_seconds,
                "success": True,
            }

        except Exception as e:
            result.errors.append(str(e))
            result.complete(success=False)

            logger.error(
                "Discovery pipeline failed", pipeline_id=pipeline_id, error=str(e)
            )

            return {"pipeline_id": pipeline_id, "error": str(e), "success": False}

        finally:
            # Cleanup
            if pipeline_id in self.running_pipelines:
                del self.running_pipelines[pipeline_id]

    async def run_analysis_pipeline(
        self,
        address: str,
        chain: str = "ethereum",
        include_technical: bool = True,
        include_sentiment: bool = True,
        progress_callback: Callable | None = None,
    ) -> dict[str, Any]:
        """Run comprehensive token analysis pipeline."""
        pipeline_id = str(uuid.uuid4())
        result = PipelineResult(pipeline_id)
        self.running_pipelines[pipeline_id] = result

        try:
            logger.info(
                "Starting analysis pipeline",
                pipeline_id=pipeline_id,
                address=address,
                chain=chain,
            )

            if progress_callback:
                await progress_callback(0.1, "Starting analysis...")

            # Step 1: Get chain info
            chain_info_result = await self._run_agent_with_circuit_breaker(
                "chain_info", "get_contract_info", address=address, chain=chain
            )

            if not chain_info_result["success"]:
                raise Exception(f"Chain info failed: {chain_info_result['error']}")

            token_data = chain_info_result["data"]
            result.agent_results["chain_info"] = chain_info_result

            if progress_callback:
                await progress_callback(0.3, "Fetching market data...")

            # Step 2: Get market data
            market_data_result = await self._run_agent_with_circuit_breaker(
                "market_data", "get_market_data", address=address, chain=chain
            )

            if market_data_result["success"]:
                token_data.update(market_data_result["data"])
                result.agent_results["market_data"] = market_data_result

            if progress_callback:
                await progress_callback(0.5, "Validating token...")

            # Step 3: Validate token
            validation_result = await self._run_agent_with_circuit_breaker(
                "validator", "validate_token", token=token_data
            )

            if not validation_result["success"]:
                raise Exception(
                    f"Token validation failed: {validation_result['error']}"
                )

            is_valid = validation_result["data"]["is_valid"]
            if not is_valid:
                raise Exception(
                    f"Token failed validation: {validation_result['data']['reason']}"
                )

            result.agent_results["validation"] = validation_result

            if progress_callback:
                await progress_callback(0.7, "Performing technical analysis...")

            # Step 4: Technical analysis (if requested)
            technical_indicators = {}
            if include_technical:
                technical_result = await self._run_agent_with_circuit_breaker(
                    "technical", "analyze_token", token_data=token_data
                )

                if technical_result["success"]:
                    technical_indicators = technical_result["data"]
                    result.agent_results["technical"] = technical_result

            if progress_callback:
                await progress_callback(0.8, "Analyzing sentiment...")

            # Step 5: Sentiment analysis (if requested)
            sentiment_data = {}
            if include_sentiment:
                sentiment_result = await self._run_agent_with_circuit_breaker(
                    "sentiment",
                    "analyze_sentiment",
                    symbol=token_data.get("symbol", ""),
                    name=token_data.get("name", ""),
                )

                if sentiment_result["success"]:
                    sentiment_data = sentiment_result["data"]
                    result.agent_results["sentiment"] = sentiment_result

            if progress_callback:
                await progress_callback(0.9, "Generating analysis...")

            # Step 6: Final analysis
            analyst_result = await self._run_agent_with_circuit_breaker(
                "analyst",
                "analyze_token",
                token_data=token_data,
                technical_indicators=technical_indicators,
                sentiment_data=sentiment_data,
            )

            if not analyst_result["success"]:
                raise Exception(f"Analysis failed: {analyst_result['error']}")

            analysis = analyst_result["data"]
            result.analyses = [analysis]
            result.agent_results["analyst"] = analyst_result

            if progress_callback:
                await progress_callback(1.0, "Analysis complete!")

            result.complete(success=True)

            logger.info(
                "Analysis pipeline completed",
                pipeline_id=pipeline_id,
                address=address,
                risk_score=analysis["risk_score"],
                alpha_score=analysis["alpha_score"],
                duration=result.duration_seconds,
            )

            return {
                "pipeline_id": pipeline_id,
                "token": token_data,
                "risk_score": analysis["risk_score"],
                "alpha_score": analysis["alpha_score"],
                "technical_indicators": technical_indicators,
                "sentiment_data": sentiment_data,
                "summary": analysis["summary"],
                "duration_seconds": result.duration_seconds,
                "success": True,
            }

        except Exception as e:
            result.errors.append(str(e))
            result.complete(success=False)

            logger.error(
                "Analysis pipeline failed",
                pipeline_id=pipeline_id,
                address=address,
                error=str(e),
            )

            return {"pipeline_id": pipeline_id, "error": str(e), "success": False}

        finally:
            # Cleanup
            if pipeline_id in self.running_pipelines:
                del self.running_pipelines[pipeline_id]

    async def analyze_token(
        self,
        token_address: str,
        analysis_types: list[str] = None,
        priority: str = "normal",
        chain_id: int = 1,
        **kwargs,
    ) -> dict[str, Any]:
        """
        Analyze a single token using specified analysis types.

        Args:
            token_address: Token contract address to analyze
            analysis_types: List of analysis types to perform
                          (validation, market_data, technical, sentiment, comprehensive)
            priority: Analysis priority (low, normal, high)
            chain_id: Blockchain ID (default: 1 for Ethereum)

        Returns:
            Dict containing analysis results
        """
        if analysis_types is None:
            analysis_types = ["validation", "market_data", "technical", "sentiment"]

        logger.info(f"Analyzing token {token_address} with types: {analysis_types}")

        results = {
            "token_address": token_address,
            "chain_id": chain_id,
            "analysis_types": analysis_types,
            "priority": priority,
            "timestamp": datetime.utcnow().isoformat(),
            "results": {},
            "success": True,
            "errors": [],
        }

        try:
            # Validation analysis
            if "validation" in analysis_types:
                try:
                    validator_agent = self.agents.get("validator")
                    if validator_agent:
                        validation_result = await validator_agent.validate_token(
                            address=token_address, chain_id=chain_id
                        )
                        results["results"]["validation"] = validation_result
                        logger.info(f"✅ Validation completed for {token_address}")
                    else:
                        results["errors"].append("Validator agent not available")
                except Exception as e:
                    error_msg = f"Validation failed: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)

            # Market data analysis
            if "market_data" in analysis_types:
                try:
                    market_agent = self.agents.get("market_data")
                    if market_agent:
                        market_result = await market_agent.get_market_data(
                            token_address=token_address, chain_id=chain_id
                        )
                        results["results"]["market_data"] = market_result
                        logger.info(f"✅ Market data completed for {token_address}")
                    else:
                        results["errors"].append("Market data agent not available")
                except Exception as e:
                    error_msg = f"Market data analysis failed: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)

            # Technical analysis
            if "technical" in analysis_types:
                try:
                    technical_agent = self.agents.get("technical")
                    if technical_agent:
                        technical_result = await technical_agent.analyze_token(
                            token_address=token_address, chain_id=chain_id
                        )
                        results["results"]["technical"] = technical_result
                        logger.info(
                            f"✅ Technical analysis completed for {token_address}"
                        )
                    else:
                        results["errors"].append("Technical agent not available")
                except Exception as e:
                    error_msg = f"Technical analysis failed: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)

            # Sentiment analysis
            if "sentiment" in analysis_types:
                try:
                    sentiment_agent = self.agents.get("sentiment")
                    if sentiment_agent:
                        sentiment_result = await sentiment_agent.analyze_sentiment(
                            token_address=token_address, chain_id=chain_id
                        )
                        results["results"]["sentiment"] = sentiment_result
                        logger.info(
                            f"✅ Sentiment analysis completed for {token_address}"
                        )
                    else:
                        results["errors"].append("Sentiment agent not available")
                except Exception as e:
                    error_msg = f"Sentiment analysis failed: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)

            # Comprehensive analysis using AnalystAgent
            if "comprehensive" in analysis_types:
                try:
                    analyst_agent = self.agents.get("analyst")
                    if analyst_agent:
                        # Get data from other analyses
                        token_data = results["results"].get("market_data", {})
                        technical_data = results["results"].get("technical", {})
                        sentiment_data = results["results"].get("sentiment", {})

                        comprehensive_result = await analyst_agent.analyze_token(
                            token_data=token_data,
                            technical_indicators=technical_data,
                            sentiment_data=sentiment_data,
                        )
                        results["results"]["comprehensive"] = comprehensive_result
                        logger.info(
                            f"✅ Comprehensive analysis completed for {token_address}"
                        )
                    else:
                        results["errors"].append("Analyst agent not available")
                except Exception as e:
                    error_msg = f"Comprehensive analysis failed: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)

            # Set overall success status
            results["success"] = len(results["errors"]) == 0
            results["analysis_count"] = len(results["results"])

            return results

        except Exception as e:
            logger.error(f"Token analysis failed for {token_address}: {e}")
            results["success"] = False
            results["errors"].append(f"Overall analysis failed: {str(e)}")
            return results

    # ==================== AGENT MANAGEMENT ====================

    async def _run_agent_with_circuit_breaker(
        self, agent_name: str, method_name: str, **kwargs
    ) -> dict[str, Any]:
        """Run agent method with circuit breaker pattern."""
        circuit_breaker = self.agent_circuit_breakers[agent_name]

        # Check if circuit breaker is open
        if self._is_circuit_breaker_open(agent_name):
            return {
                "success": False,
                "error": f"Circuit breaker open for agent {agent_name}",
                "agent": agent_name,
                "method": method_name,
            }

        start_time = datetime.utcnow()

        try:
            agent = self.agents[agent_name]
            method = getattr(agent, method_name)

            result = await method(**kwargs)

            # Reset circuit breaker on success
            circuit_breaker["failures"] = 0
            circuit_breaker["last_failure"] = None

            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            # Record metrics
            await self.metrics_collector.record_agent_execution(
                agent_name=agent_name,
                method_name=method_name,
                execution_time_ms=execution_time,
                success=True,
            )

            return {
                "success": True,
                "data": result,
                "agent": agent_name,
                "method": method_name,
                "execution_time_ms": execution_time,
            }

        except Exception as e:
            # Update circuit breaker
            circuit_breaker["failures"] += 1
            circuit_breaker["last_failure"] = datetime.utcnow()

            execution_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            # Record metrics
            await self.metrics_collector.record_agent_execution(
                agent_name=agent_name,
                method_name=method_name,
                execution_time_ms=execution_time,
                success=False,
                error=str(e),
            )

            logger.error(
                "Agent execution failed",
                agent=agent_name,
                method=method_name,
                error=str(e),
                failures=circuit_breaker["failures"],
            )

            return {
                "success": False,
                "error": str(e),
                "agent": agent_name,
                "method": method_name,
                "execution_time_ms": execution_time,
            }

    def _is_circuit_breaker_open(self, agent_name: str) -> bool:
        """Check if circuit breaker is open for an agent."""
        circuit_breaker = self.agent_circuit_breakers[agent_name]

        if circuit_breaker["failures"] < circuit_breaker["threshold"]:
            return False

        if circuit_breaker["last_failure"] is None:
            return False

        time_since_failure = (
            datetime.utcnow() - circuit_breaker["last_failure"]
        ).total_seconds()
        return time_since_failure < circuit_breaker["timeout"]

    # ==================== MONITORING & HEALTH ====================

    async def health_check(self) -> bool:
        """Check health of all agents."""
        try:
            all_healthy = True

            for name, agent in self.agents.items():
                try:
                    healthy = await agent.health_check()
                    if not healthy:
                        all_healthy = False
                        logger.warning(f"Agent {name} health check failed")
                except Exception as e:
                    all_healthy = False
                    logger.error(f"Agent {name} health check error", error=str(e))

            return all_healthy

        except Exception as e:
            logger.error("Coordinator health check failed", error=str(e))
            return False

    async def get_agent_status(self) -> dict[str, Any]:
        """Get status of all agents."""
        status = {}

        for name, agent in self.agents.items():
            try:
                circuit_breaker = self.agent_circuit_breakers[name]
                agent_status = {
                    "healthy": await agent.health_check(),
                    "circuit_breaker": {
                        "failures": circuit_breaker["failures"],
                        "is_open": self._is_circuit_breaker_open(name),
                        "last_failure": circuit_breaker["last_failure"].isoformat()
                        if circuit_breaker["last_failure"]
                        else None,
                    },
                }

                # Add agent-specific metrics if available
                if hasattr(agent, "get_metrics"):
                    agent_status["metrics"] = await agent.get_metrics()

                status[name] = agent_status

            except Exception as e:
                status[name] = {"error": str(e)}

        return status

    # ==================== UTILITY METHODS ====================

    async def get_market_data(
        self, address: str, chain: str = "ethereum", timeframe: str = "24h"
    ) -> dict[str, Any]:
        """Get market data for a token."""
        result = await self._run_agent_with_circuit_breaker(
            "market_data",
            "get_market_data",
            address=address,
            chain=chain,
            timeframe=timeframe,
        )

        if result["success"]:
            return result["data"]
        else:
            raise Exception(f"Failed to get market data: {result['error']}")

    async def get_token_summary(self, address: str) -> dict[str, Any]:
        """Get basic token summary."""
        # Try cache first
        cache_key = f"token_summary:{address}"
        cached = await self.cache_manager.get(cache_key, "tokens")

        if cached:
            return cached

        # Get from database
        token_data = await self.db_manager.get_token(address)

        if token_data:
            # Cache result
            await self.cache_manager.set(cache_key, token_data, "tokens", ttl=300)
            return token_data

        # Fetch fresh data
        result = await self._run_agent_with_circuit_breaker(
            "chain_info", "get_contract_info", address=address, chain="ethereum"
        )

        if result["success"]:
            summary = result["data"]
            await self.cache_manager.set(cache_key, summary, "tokens", ttl=300)
            return summary

        raise Exception(f"Failed to get token summary: {result['error']}")

    async def start_monitoring(
        self, addresses: list[str], chain: str = "ethereum", interval_seconds: int = 60
    ) -> str:
        """Start monitoring tokens."""
        monitor_id = str(uuid.uuid4())

        # Create monitoring task
        task = asyncio.create_task(
            self._monitor_tokens(monitor_id, addresses, chain, interval_seconds)
        )

        self.monitoring_tasks.append(task)

        logger.info(
            "Started token monitoring",
            monitor_id=monitor_id,
            addresses=addresses,
            interval=interval_seconds,
        )

        return monitor_id

    async def _monitor_tokens(
        self, monitor_id: str, addresses: list[str], chain: str, interval_seconds: int
    ):
        """Monitor tokens continuously."""
        try:
            while True:
                for address in addresses:
                    try:
                        # Get current market data
                        market_data = await self.get_market_data(address, chain)

                        # Check for alerts (price changes, volume spikes, etc.)
                        await self._check_token_alerts(address, market_data)

                    except Exception as e:
                        logger.error(
                            "Error monitoring token",
                            monitor_id=monitor_id,
                            address=address,
                            error=str(e),
                        )

                await asyncio.sleep(interval_seconds)

        except asyncio.CancelledError:
            logger.info("Token monitoring cancelled", monitor_id=monitor_id)
            raise
        except Exception as e:
            logger.error("Token monitoring failed", monitor_id=monitor_id, error=str(e))

    async def _check_token_alerts(self, address: str, market_data: dict[str, Any]):
        """Check for alert conditions."""
        # Implementation would check for significant price changes,
        # volume spikes, etc. and trigger alerts
        pass

    async def _start_scheduler(self):
        """Start the scheduler agent."""
        try:
            scheduler = self.agents["scheduler"]
            task = asyncio.create_task(scheduler.run())
            self.monitoring_tasks.append(task)
            logger.info("Scheduler started")
        except Exception as e:
            logger.error("Failed to start scheduler", error=str(e))
